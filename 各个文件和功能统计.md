# 后端业务文件功能清单 (backend_business_files.md)

## 项目概述
- **项目名称**: 活动宝API后端系统
- **技术栈**: PHP + MySQL + 自定义MVC框架
- **项目路径**: `huodongbao_api`
- **数据库操作**: 使用自定义Db类，支持参数化查询
- **认证机制**: Controller::auth($uid, $token)

## Controller文件总览

| 文件名 | 主要业务职责 | 文件大小(行数) | 主要数据表 |
|--------|-------------|---------------|-----------|
| User.php | 用户管理、认证、佣金、提现 | 5564行 | user, user_bank, user_addr, commission_log |
| Huodong.php | 活动管理、报名、评价、签到 | 3202行 | huodong, huodong_order, huodong_pingjia |
| Pay.php | 支付处理、订单管理 | 1596行 | huodong_order, goods_order, huiyuan_order |
| Admin.php | 后台管理功能 | 待分析 | 多表管理 |
| Branchpresident.php | 分会长管理 | 待分析 | user_branch, branch_change_log |
| Config.php | 系统配置、基础数据 | 待分析 | gaode_district, huodong_type |
| Goods.php | 商品管理 | 待分析 | goods, goods_order |
| Index.php | 首页数据、轮播图 | 待分析 | lunbotu, huodong |
| Lunbotu.php | 轮播图管理 | 待分析 | lunbotu |
| World.php | 社交功能、动态 | 待分析 | world_feed, world_comment |
| Dalibao.php | 大礼包功能 | 待分析 | dalibao, dalibao_order |
| Html.php | 静态页面 | 待分析 | - |

---

## 详细功能分析

### 3. Pay.php - 支付处理核心
**文件路径**: `huodongbao_api/controller/Pay.php`
**业务职责**: 支付处理、微信支付、余额支付、退款处理

#### 主要Public方法 (12个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `yue_pay()` | POST | uid, token, type, order_id, money | 余额支付 | user, huodong_order, goods_order, huiyuan_order |
| `weixin_pay()` | POST | uid, token, type, order_id, money, pay_type | 微信支付 | user, huodong_order, goods_order, huiyuan_order |
| `get_weixinpay_sign()` | POST | prepay_id | 获取微信支付签名 | - |
| `weixinpay_notify()` | POST | - | 微信支付回调通知 | huodong_order, goods_order, huiyuan_order |
| `weixinpay_tuikuan_notify()` | POST | - | 微信退款回调通知 | huodong_order, goods_order |
| `get_server_time()` | GET | - | 获取服务器时间(调试用) | - |
| `test_log_write_error()` | GET | - | 测试日志写入功能 | - |
| `auto_get_wechat_cert()` | GET | serial | 自动获取微信平台证书 | - |
| `check_cert_serial()` | GET | wechat_serial | 检查证书序列号 | - |
| `get_wechat_cert_info()` | GET | - | 获取微信证书信息 | - |

#### 支付类型说明
- **type=1**: 商城支付
- **type=2**: 活动报名
- **type=3**: 购买会员
- **type=4**: 充值/大礼包支付

#### 重要私有方法
| 方法名 | 功能描述 |
|--------|----------|
| `logWriteError()` | 记录支付相关错误日志 |

---

### 4. Config.php - 系统配置核心
**文件路径**: `huodongbao_api/controller/Config.php`
**业务职责**: 系统配置管理、地区数据、图片上传、二维码生成

#### 主要Public方法 (32个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `app()` | GET | - | 获取APP配置信息 | config, img_config, video_config |
| `splash_media()` | GET | - | 获取开屏媒体配置 | media_config |
| `profile_bg_media()` | GET | - | 获取个人中心背景媒体 | media_config |
| `pop()` | GET | - | 获取弹窗配置 | pop |
| `chongzhi_list()` | GET | - | 获取充值金额列表 | user_chongzhi |
| `send_sms()` | POST | mobile, type | 发送短信验证码 | - |
| `get_china()` | GET | - | 获取中国地区数据 | china |
| `get_city_list()` | GET | search | 获取城市列表 | gaode_district |
| `get_shengshiqu_id()` | POST | name, type | 根据名称获取地区ID | china |
| `get_area_by_adcode()` | POST | adcode | 根据adcode获取地区信息 | gaode_district |
| `fetch_gaode_district_data()` | GET | key, keywords, subdistrict | 获取高德地区数据 | - |
| `import_gaode_data_to_db()` | POST | key, clean | 导入高德数据到数据库 | gaode_district |
| `create_china_adcode_mapping()` | POST | - | 创建china表与adcode映射 | china, gaode_district |
| `migrate_huodong_table()` | POST | - | 迁移活动表地区字段 | huodong |
| `migrate_user_addr_table()` | POST | - | 迁移用户地址表地区字段 | user_addr |
| `get_migration_status()` | GET | - | 查看迁移状态 | migration_status |
| `check_table_status()` | GET | - | 检查数据表状态 | gaode_district, china |
| `generate_gaode_china_js()` | GET | - | 生成高德版本china.js | gaode_district |
| `download_gaode_china_js()` | GET | - | 下载china.js文件 | gaode_district |
| `reset_migration_data()` | POST | - | 清理迁移数据 | migration_status |
| `execute_full_migration()` | POST | key | 执行完整迁移 | 多表 |
| `replace_china_with_gaode()` | POST | - | 替换china表为高德数据 | china, gaode_district |
| `get_deep_link()` | POST | path, query | 获取小程序深度链接 | - |
| `upload_img()` | POST | img | 图片上传 | - |
| `qrcode()` | GET | contents, base64_decode, urldecode | 生成二维码 | - |
| `qrcodeText()` | GET | contents, base64_decode, urldecode | 生成带文字二维码 | - |
| `phpinfo()` | GET | - | 查看PHP信息 | - |
| `wxacode()` | GET | path, query | 生成小程序码 | - |
| `clear()` | POST | uid, token, column | 清理缓存 | - |
| `get_splash_config()` | GET | - | 获取启动页配置 | config |

#### 配置类型说明
- **基础配置**: app_name, 充值提现设置, 会员价格等
- **媒体配置**: 开屏图片/视频, 背景媒体等
- **地区数据**: 支持高德地图API集成, 省市区三级联动
- **迁移工具**: 数据表结构迁移, 地区数据标准化

---

### 5. Admin.php - 后台管理
**文件路径**: `huodongbao_api/controller/Admin.php`
**业务职责**: 后台管理功能、分会长审核

#### 主要Public方法 (2个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `branch_president_review()` | POST | application_id, status, comment | 分会长申请审核 | branch_president_applications |

---

### 6. Branchpresident.php - 分会长管理
**文件路径**: `huodongbao_api/controller/Branchpresident.php`
**业务职责**: 分会长申请、活动审核、成员管理、佣金查看

#### 主要Public方法 (8个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `apply()` | POST | uid, token, 分会信息, 申请理由 | 申请成为分会长 | branch_president_applications |
| `pending_activities()` | POST | uid, token, page, page_size | 获取待审核活动 | huodong |
| `review_activity()` | POST | uid, token, huodong_id, status, comment | 审核活动 | huodong |
| `get_stats()` | POST | uid, token | 获取分会统计数据 | user, commission_log, huodong |
| `get_commission()` | POST | uid, token, page, page_size | 获取佣金记录 | commission_log |
| `get_members()` | POST | uid, token, keyword, page, page_size | 获取分会成员 | user |
| `get_activities()` | POST | uid, token, page, page_size, status | 获取分会活动 | huodong |

---

### 7. Goods.php - 商品管理
**文件路径**: `huodongbao_api/controller/Goods.php`
**业务职责**: 商品展示、购物车、订单管理、评价系统

#### 主要Public方法 (18个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `get_types()` | GET | - | 获取商品分类 | goods_type |
| `get_list()` | POST | uid, token, 筛选参数 | 获取商品列表 | goods |
| `get_goods_info()` | POST | uid, token, goods_id, guige | 获取商品详情 | goods, goods_guige |
| `get_goods_kucun()` | POST | uid, token, goods_id | 获取商品库存 | goods_guige |
| `add_car()` | POST | uid, token, goods_id, guige_id, num | 添加到购物车 | goods_car |
| `del_car()` | POST | uid, token, ids | 删除购物车商品 | goods_car |
| `get_car_list()` | POST | uid, token | 获取购物车列表 | goods_car |
| `add_order()` | POST | uid, token, goods_info, addr_id | 创建商品订单 | goods_order |
| `get_order_list()` | POST | uid, token, page, page_size, status, is_pingjia | 获取订单列表 | goods_order |
| `add_pingjia()` | POST | uid, token, order_id, pingjia_data | 添加商品评价 | goods_pingjia |
| `get_goods_pingjia()` | POST | uid, token, goods_id, 筛选参数 | 获取商品评价 | goods_pingjia |
| `get_my_goods_pingjia()` | POST | uid, token, page, page_size | 获取我的评价 | goods_pingjia |
| `cancel_order()` | POST | uid, token, order_id | 取消订单 | goods_order |
| `queren_shouhuo()` | POST | uid, token, order_id | 确认收货 | goods_order |
| `yanchi_shouhuo()` | POST | uid, token, order_id | 延迟收货 | goods_order |
| `shenqing_tuikuan()` | POST | uid, token, order_id, msg | 申请退款 | goods_order |

---

### 8. World.php - 社交功能核心
**文件路径**: `huodongbao_api/controller/World.php`
**业务职责**: 动态发布、每日卡片、摘录功能、点赞评论、社交互动

#### 主要Public方法 (27个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `get_daily_cards()` | GET | startDate, endDate, uid, token | 获取每日卡片 | world_card |
| `get_card_detail()` | GET | id, uid, token | 获取卡片详情 | world_card |
| `get_cards()` | GET | page, page_size, uid, token | 获取卡片列表 | world_card |
| `get_feeds()` | GET | page, page_size, uid, token, 筛选参数 | 获取动态列表 | world_feed |
| `get_feed_detail()` | GET | id, uid, token | 获取动态详情 | world_feed |
| `publish_feed()` | POST | uid, token, content, images, location, tags, privacy, type | 发布动态 | world_feed |
| `publish_card()` | POST | uid, token, card_date, description, author, background_image_url | 发布卡片 | world_card |
| `like_feed()` | POST | uid, token, id | 点赞动态 | world_zan |
| `comment_feed()` | POST | uid, token, feed_id, content, parent_id | 评论动态 | world_comment |
| `create_quote()` | POST | uid, token, content, author, source, tags, privacy, allow_official, images | 创建摘录 | world_quote |
| `like_card()` | POST | uid, token, id | 点赞卡片 | world_zan |
| `favorite_card()` | POST | uid, token, id | 收藏卡片 | world_shoucang |
| `favorite_feed()` | POST | uid, token, id | 收藏动态 | world_shoucang |
| `comment_card()` | POST | uid, token, card_id, content, parent_id | 评论卡片 | world_comment |
| `get_card_comments()` | GET | card_id, page, page_size, uid, token, sort_type | 获取卡片评论 | world_comment |
| `get_feed_comments()` | GET | feed_id, page, page_size, uid, token, sort_type | 获取动态评论 | world_comment |
| `like_comment()` | POST | uid, token, comment_id, comment_type | 点赞评论 | world_comment_zan |
| `get_comment_like_status()` | POST | uid, token, comment_ids, comment_type | 获取评论点赞状态 | world_comment_zan |
| `delete_feed()` | POST | uid, token, feed_id | 删除动态 | world_feed |
| `delete_comment()` | POST | uid, token, comment_id, comment_type | 删除评论 | world_comment |
| `get_random_quotes_for_card()` | POST | uid, token | 获取随机摘录 | world_quote |
| `get_quotes()` | GET | page, page_size, uid, token, user_id | 获取摘录列表 | world_quote |
| `get_quote_detail()` | POST | uid, token, quote_id | 获取摘录详情 | world_quote |
| `get_quote_comments()` | POST | uid, token, quote_id, page, page_size | 获取摘录评论 | world_comment |

#### 社交功能类型
- **动态(Feed)**: 用户发布的文字、图片动态
- **卡片(Card)**: 每日精选内容卡片
- **摘录(Quote)**: 用户创建的名言摘录
- **评论系统**: 支持多级评论和点赞
- **收藏点赞**: 完整的社交互动功能

---

### 9. Index.php - 首页入口
**文件路径**: `huodongbao_api/controller/Index.php`
**业务职责**: 首页数据、调试功能

#### 主要Public方法 (5个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `index()` | GET | - | 首页基础信息 | - |
| `debug_add_huodong()` | POST | - | 调试添加活动 | huodong |
| `test()` | GET | - | 测试功能 | - |

---

### 10. Lunbotu.php - 轮播图管理
**文件路径**: `huodongbao_api/controller/Lunbotu.php`
**业务职责**: 轮播图展示

#### 主要Public方法 (3个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `index()` | GET | uid, token, position | 获取轮播图列表 | lunbotu |

#### 轮播图位置说明
- **position=1**: 首页轮播图
- **position=2**: 其他位置轮播图

---

### 11. Dalibao.php - 大礼包功能
**文件路径**: `huodongbao_api/controller/Dalibao.php`
**业务职责**: 大礼包商品管理、订单处理

#### 主要Public方法 (7个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `get_info()` | GET | uid, token | 获取大礼包信息 | dalibao |
| `add_order()` | POST | uid, token, addr_id | 创建大礼包订单 | dalibao_order |
| `get_order_list()` | POST | uid, token, page, page_size, status | 获取订单列表 | dalibao_order |
| `cancel_order()` | POST | uid, token, order_id | 取消订单 | dalibao_order |
| `queren_shouhuo()` | POST | uid, token, order_id | 确认收货 | dalibao_order |

---

### 12. Html.php - 静态页面
**文件路径**: `huodongbao_api/controller/Html.php`
**业务职责**: 静态HTML页面展示

#### 主要Public方法 (3个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `index()` | GET | type | 获取HTML页面内容 | html |

#### 页面类型说明
- **type=1**: 用户协议
- **type=2**: 隐私政策
- **type=3**: 其他静态页面

---

## 业务模块分类总结

### 🔐 用户管理模块
- **User.php**: 用户注册登录、个人信息、认证授权
- **Admin.php**: 后台管理、审核功能

### 🎯 活动管理模块
- **Huodong.php**: 活动发布、报名、评价、签到
- **Branchpresident.php**: 分会长管理、活动审核

### 💰 支付交易模块
- **Pay.php**: 支付处理、微信支付、余额支付
- **User.php**: 佣金系统、提现管理、账单记录

### 🛒 商品模块
- **Goods.php**: 商品展示、购物车、订单管理
- **Dalibao.php**: 大礼包特殊商品

### 🌍 社交模块
- **World.php**: 动态发布、卡片、摘录、评论点赞

### ⚙️ 系统配置模块
- **Config.php**: 系统配置、地区数据、图片上传
- **Index.php**: 首页数据
- **Lunbotu.php**: 轮播图管理
- **Html.php**: 静态页面

---

## 数据库表关系图

### 核心业务表
```
用户相关:
├── user (用户基础信息)
├── user_bank (银行卡)
├── user_addr (收货地址)
├── user_label (用户标签)
├── user_img (用户照片)
├── user_guanzhu (关注关系)
├── user_notifications (通知)
└── user_branch (分会信息)

活动相关:
├── huodong (活动基础信息)
├── huodong_order (活动报名订单)
├── huodong_pingjia (活动评价)
├── huodong_shoucang (活动收藏)
├── huodong_zan (活动点赞)
├── huodong_checkin (活动签到)
├── huodong_type (活动分类)
└── activity_photos (活动相册)

商品相关:
├── goods (商品信息)
├── goods_order (商品订单)
├── goods_car (购物车)
├── goods_pingjia (商品评价)
├── goods_type (商品分类)
├── goods_guige (商品规格)
├── dalibao (大礼包)
└── dalibao_order (大礼包订单)

社交相关:
├── world_feed (动态)
├── world_card (卡片)
├── world_quote (摘录)
├── world_comment (评论)
├── world_zan (点赞)
└── world_shoucang (收藏)

财务相关:
├── commission_log (佣金记录)
├── activity_income_log (活动收入)
├── user_tixian (提现申请)
├── user_zhangdan (账单记录)
├── user_chongzhi (充值记录)
└── huiyuan_order (会员订单)
```

### 系统配置表
```
配置管理:
├── config (系统配置)
├── img_config (图片配置)
├── video_config (视频配置)
├── media_config (媒体配置)
├── pop (弹窗配置)
├── lunbotu (轮播图)
├── html (静态页面)
├── gaode_district (高德地区数据)
├── china (中国地区数据)
└── migration_status (迁移状态)

权限管理:
├── user_branch (分会信息)
├── branch_president_applications (分会长申请)
├── branch_change_log (分会长变更记录)
└── commission_configs (佣金配置)
```

---

## API接口路径规范

### 路径格式
```
https://api.linqingkeji.com/[Controller]/[Method]
```

### 主要接口分类
- **用户相关**: `/User/login`, `/User/get_user_info`, `/User/update`
- **活动相关**: `/Huodong/get_list`, `/Huodong/add_huodong`, `/Huodong/add_baoming`
- **支付相关**: `/Pay/weixin_pay`, `/Pay/yue_pay`, `/Pay/weixinpay_notify`
- **商品相关**: `/Goods/get_list`, `/Goods/add_order`, `/Goods/add_car`
- **社交相关**: `/World/get_feeds`, `/World/publish_feed`, `/World/like_feed`
- **配置相关**: `/Config/app`, `/Config/get_city_list`, `/Config/upload_img`

---

## 技术特点总结

### 🔧 技术架构
- **框架**: 自定义PHP MVC框架
- **数据库**: MySQL + 自定义Db类
- **缓存**: Redis缓存支持
- **认证**: Token-based认证机制

### 📊 数据处理
- **参数化查询**: 使用prepareParam()防止SQL注入
- **事务管理**: 支持数据库事务操作
- **错误日志**: 完善的错误日志记录机制
- **数据验证**: 统一的参数验证函数

### 🔐 安全机制
- **身份验证**: Controller::auth($uid, $token)
- **参数验证**: check()函数进行类型和格式验证
- **SQL防注入**: 强制使用参数化查询
- **权限控制**: 基于role_type的角色权限系统

### 🚀 性能优化
- **缓存策略**: Redis缓存热点数据
- **分页查询**: 统一的分页参数处理
- **索引优化**: 数据库查询优化
- **并发处理**: 支持高并发访问

---

**文档生成时间**: 2025-07-29
**API版本**: v1.0
**维护状态**: 活跃开发中

### 1. User.php - 用户管理核心
**文件路径**: `huodongbao_api/controller/User.php`  
**业务职责**: 用户注册登录、个人信息管理、佣金系统、提现管理、社交功能

#### 主要Public方法 (60个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `login()` | POST | code, pid, nickname, avatar, mobile, sex, city | 微信登录/注册 | user |
| `get_user_info()` | POST | uid, token | 获取用户详细信息 | user, user_label, user_img |
| `update_mobile()` | POST | uid, token, code | 更新手机号(微信授权) | user |
| `update_mobile_manual()` | POST | uid, token, mobile | 手动更新手机号 | user |
| `update()` | POST | uid, token, [其他字段] | 更新用户基本信息 | user |
| `add_label()` | POST | uid, token, label | 添加用户标签 | user_label |
| `del_label()` | POST | uid, token, id | 删除用户标签 | user_label |
| `add_img()` | POST | uid, token, img_url | 添加用户照片 | user_img |
| `del_img()` | POST | uid, token, ids | 删除用户照片 | user_img |
| `add_addr()` | POST | uid, token, 地址信息 | 添加收货地址 | user_addr |
| `del_addr()` | POST | uid, token, ids | 删除收货地址 | user_addr |
| `get_addr_list()` | POST | uid, token | 获取地址列表 | user_addr |
| `set_default_addr()` | POST | uid, token, id | 设置默认地址 | user_addr |
| `bank_add()` | POST | uid, token, 银行信息 | 添加银行卡 | user_bank |
| `bank_del()` | POST | uid, token, ids | 删除银行卡 | user_bank |
| `bank_list()` | POST | uid, token | 获取银行卡列表 | user_bank |
| `set_default_bank()` | POST | uid, token, id | 设置默认银行卡 | user_bank |
| `tixian()` | POST | uid, token, bank_id, money | 申请提现 | user_tixian, user_zhangdan |
| `get_tixian_list()` | POST | uid, token, page, page_size, status | 获取提现记录 | user_tixian |
| `get_yongjin_log()` | POST | uid, token, page, page_size, type, commission_type | 获取佣金记录 | commission_log |
| `get_chongzhi_log()` | POST | uid, token, page, page_size, status | 获取充值记录 | user_chongzhi |
| `get_fenxiang_log()` | POST | uid, token, page, page_size, type | 获取分享记录 | user_fenxiang |
| `fenxiang_event()` | POST | uid, token, type, item_id | 记录分享事件 | user_fenxiang |
| `get_zhangdan()` | POST | uid, token, page, page_size, type | 获取账单记录 | user_zhangdan |
| `add_chongzhi_order()` | POST | uid, token, money | 创建充值订单 | user_chongzhi |
| `add_huiyuan_order()` | POST | uid, token | 创建会员订单 | huiyuan_order |
| `guanzhu_add()` | POST | uid, token, to_uid | 关注用户 | user_guanzhu |
| `guanzhu_del()` | POST | uid, token, to_uid | 取消关注 | user_guanzhu |
| `guanzhu_check()` | POST | uid, token, to_uid | 检查关注状态 | user_guanzhu |
| `get_guanzhu_list()` | POST | uid, token, page, page_size | 获取关注列表 | user_guanzhu |
| `get_fans_list()` | POST | uid, token, page, page_size | 获取粉丝列表 | user_guanzhu |
| `get_shejiao_list()` | POST | uid, token, page, page_size | 获取社交推荐列表 | user |
| `get_other_user_info()` | POST | uid, token, to_uid, include_details | 获取他人信息 | user, huodong, world_feed |
| `get_daijiesuan_status()` | POST | uid, token | 获取待结算状态 | commission_log, activity_income_log |
| `get_xiaji_user()` | POST | uid, token, to_uid, page, page_size | 获取下级用户 | user |
| `settle_member_commission()` | - | order_info, use_external_transaction | 结算会员佣金 | commission_log, user_zhangdan |
| `get_user_likes()` | POST | uid, token, page, page_size | 获取用户点赞 | huodong_zan, world_zan |
| `get_user_favorites()` | POST | uid, token, page, page_size, type | 获取用户收藏 | huodong_shoucang, world_shoucang |
| `get_user_published()` | POST | uid, token, page, page_size, type | 获取用户发布内容 | huodong, world_feed |
| `get_user_comments()` | POST | uid, token, page, page_size, type | 获取用户评论 | huodong_pingjia, world_comment |

#### 通知系统方法

| 方法名 | 功能描述 | 涉及数据表 |
|--------|----------|-----------|
| `get_notifications()` | 获取通知列表 | user_notifications |
| `mark_notification_read()` | 标记通知已读 | user_notifications |
| `get_unread_count()` | 获取未读通知数量 | user_notifications |
| `create_global_notification()` | 创建全局通知 | user_notifications |
| `create_branch_notification()` | 创建分会通知 | user_notifications |

#### 会员体验分享功能

| 方法名 | 功能描述 | 涉及数据表 |
|--------|----------|-----------|
| `create_trial_share()` | 创建体验分享 | trial_member_shares |
| `get_trial_info()` | 获取体验信息 | trial_member_shares |
| `claim_trial_member()` | 领取体验会员 | trial_member_shares, user |
| `get_share_records()` | 获取分享记录 | trial_member_shares |

#### 积分系统

| 方法名 | 功能描述 | 涉及数据表 |
|--------|----------|-----------|
| `get_points_log()` | 获取积分记录 | user_points_log |

#### 分会管理相关

| 方法名 | 功能描述 | 涉及数据表 |
|--------|----------|-----------|
| `update_city()` | 更新用户城市 | user |
| `get_branch_leader_info()` | 获取分会长信息 | user_branch, user |
| `handleFirstTimeMemberBranchAssignment()` | 处理首次会员分会分配 | user, user_branch |

#### 活动收入管理

| 方法名 | 功能描述 | 涉及数据表 |
|--------|----------|-----------|
| `get_activity_income_status()` | 获取活动收入状态 | activity_income_log |
| `apply_activity_income_withdraw()` | 申请活动收入提现 | activity_income_log, user_tixian |
| `get_activity_income_list()` | 获取活动收入列表 | activity_income_log |

#### 重要私有方法

| 方法名 | 功能描述 |
|--------|----------|
| `logWriteError()` | 记录错误日志 |
| `_get_user_info()` | 获取用户基础信息 |
| `_get_user_labels()` | 获取用户标签 |
| `_get_user_imgs()` | 获取用户照片 |

---

### 2. Huodong.php - 活动管理核心
**文件路径**: `huodongbao_api/controller/Huodong.php`  
**业务职责**: 活动发布、报名管理、评价系统、签到功能、活动相册

#### 主要Public方法 (26个)

| 方法名 | HTTP方法 | 参数 | 功能描述 | 涉及数据表 |
|--------|----------|------|----------|-----------|
| `get_type()` | GET | - | 获取活动分类 | huodong_type |
| `add_huodong()` | POST | uid, token, 活动信息 | 发布活动 | huodong |
| `update_huodong()` | POST | uid, token, huodong_id, 活动信息 | 更新活动 | huodong |
| `cancel_huodong()` | POST | uid, token, huodong_id | 取消活动 | huodong |
| `get_list()` | GET | 筛选参数 | 获取活动列表 | huodong |
| `get_my_list()` | POST | uid, token, page, page_size, type | 获取我的活动 | huodong, huodong_pingjia |
| `get_info()` | POST | uid, token, huodong_id | 获取活动详情 | huodong |
| `add_baoming()` | POST | uid, token, huodong_id, 联系信息 | 活动报名 | huodong_order |
| `hexiao_baoming()` | POST | uid, token, 核销信息 | 核销报名 | huodong_order |
| `cancel_baoming()` | POST | uid, token, order_id | 取消报名 | huodong_order |
| `delete_baoming()` | POST | uid, token, order_id | 删除报名 | huodong_order |
| `get_baoming_list()` | POST | uid, token, huodong_id, page, page_size | 获取报名列表 | huodong_order |
| `get_baoming_list_public()` | POST | uid, token, huodong_id, page, page_size | 获取公开报名列表 | huodong_order |
| `shoucang_add()` | POST | uid, token, huodong_id | 收藏活动 | huodong_shoucang |
| `shoucang_del()` | POST | uid, token, ids | 取消收藏 | huodong_shoucang |
| `zan_add()` | POST | uid, token, huodong_id | 点赞活动 | huodong_zan |
| `zan_del()` | POST | uid, token, huodong_ids | 取消点赞 | huodong_zan |
| `add_pingjia()` | POST | uid, token, huodong_id, contents, imgs_url | 添加评价 | huodong_pingjia |
| `get_pingjia()` | GET | huodong_id, page, page_size | 获取评价列表 | huodong_pingjia |
| `checkin()` | POST | uid, token, huodong_id, lat, lng | 活动签到 | huodong_checkin |
| `settleActivityIncome()` | - | huodong_id | 结算活动收入 | huodong, activity_income_log |
| `get_activity_photos()` | GET | activity_id, page, page_size | 获取活动相册 | activity_photos |
| `upload_activity_photo()` | POST | uid, token, activity_id, photo_url | 上传活动照片 | activity_photos |
| `delete_activity_photo()` | POST | uid, token, photo_id | 删除活动照片 | activity_photos |

#### 重要私有方法

| 方法名 | 功能描述 |
|--------|----------|
| `logWriteError()` | 记录活动相关错误日志 |
| `calculateDistance()` | 计算地理距离 |
| `getClientIP()` | 获取客户端IP |

---

