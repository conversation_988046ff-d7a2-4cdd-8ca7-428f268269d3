import h from "@/utils/request";
import { store } from "@/store";

// 上传图片
export const upload_img = (e, name) => {
  // 确保用户已登录
  const userInfo = store().$state.userInfo;
  const data = {
    uid: userInfo?.uid || 0,
    token: userInfo?.token || ''
  };
  return h.u("config/upload_img", e, name ? name : "img", { data });
};

// APP相关配置
export const configapp = () => h.g("config/app");


export const getSplashMedia = () => h.g("config/splash_media");


// 获取个人中心背景媒体配置
export const getProfileBgMedia = () => h.g("config/profile_bg_media");

// 充值列表
export const configchongzhi_list = () => h.g("config/chongzhi_list");

// 获取省市区
export const configget_china = () => h.g("config/get_china");

// {{ AURA-X: Modify - 增强城市列表获取接口，支持拼音排序和分组. Confirmed via 寸止. }}
// 获取城市列表（用于城市选择页面）
// @param search 搜索关键词
// @param group_by_letter 是否按首字母分组返回（1=分组，0=列表，默认0）
export const getCityList = (search = '', group_by_letter = 0) => h.g("config/get_city_list", { search, group_by_letter });

// 获取手机号
export const setPhone = e => h.p("user/update_mobile", e);

// 发短信
export const configsend_sms = e => h.p("config/send_sms", e);

// 省市区换编号
export const configget_shengshiqu_id = e => h.p("config/get_shengshiqu_id", e);

// 通过位置信息转换高德坐标
export const getCoordinate = e => h.getCoordinate(e);

// 通过高德坐标获取位置信息
export const getAddr = e => h.getAddr(e);

// 获取商品类型
export const goodsget_types = () => h.g("goods/get_types");

// 商品列表
export const goodsget_list = e => h.p("goods/get_list", e);

// 商品详情
export const goodsget_goods_info = e => h.p("goods/get_goods_info", e);

// 库存数量
export const goodsget_goods_kucun = e => h.p("goods/get_goods_kucun", e);

// 添加购物车
export const goodsadd_car = e => h.p("goods/add_car", e);

// 删除购物车
export const goodsdel_car = e => h.p("goods/del_car", e);

// 获取购物车列表
export const goodsget_car_list = e => h.p("goods/get_car_list", e);

// 商品下单
export const goodsadd_order = e => h.p("goods/add_order", e);

// 获取我的下单列表
export const goodsget_order_list = e => h.p("goods/get_order_list", e);

// 添加评价
export const goodsadd_pingjia = e => h.p("goods/add_pingjia", e);

// 获取商品评价
export const goodsget_goods_pingjia = e => h.p("goods/get_goods_pingjia", e);

// 获取我的评价
export const goodsget_my_goods_pingjia = e =>
  h.p("goods/get_my_goods_pingjia", e);

// 取消订单
export const goodscancel_order = e => h.p("goods/cancel_order", e);

// 确认收货
export const goodsqueren_shouhuo = e => h.p("goods/queren_shouhuo", e);

// 延迟收货
export const goodsyanchi_shouhuo = e => h.p("goods/yanchi_shouhuo", e);

// 申请退款
export const goodsshenqing_tuikuan = e => h.p("goods/shenqing_tuikuan", e);

// 获取富文本
export const htmlindex = e => h.p("html/index", e);

// 活动分类
export const huodongget_type = e => h.g("huodong/get_type", e);

// 添加活动信息
export const huodongadd_huodong = e => h.p("huodong/add_huodong", e);

// 修改活动信息
export const huodongupdate_huodong = e => h.p("huodong/update_huodong", e);

// 调试活动发布参数
export const debug_add_huodong = e => h.p("index/debug_add_huodong", e);

// 取消活动
export const huodongcancel_huodong = e => h.p("huodong/cancel_huodong", e);

// 获取活动列表
export const huodongget_list = e => h.p("huodong/get_list", e);

// 获取和我相关的活动列表
export const huodongget_my_list = e => h.p("huodong/get_my_list", e);

// 活动详情
export const huodongget_info = e => h.p("huodong/get_info", e);

// 报名下单
export const huodongadd_baoming = e => h.p("huodong/add_baoming", e);

// 取消报名
export const huodongcancel_baoming = e => h.p("huodong/cancel_baoming", e);

// 报名失败，删除报名
export const huodongdelete_baoming = e => h.p("huodong/delete_baoming", e);

// 发布者获取活动报名列表
export const huodongget_baoming_list = e => h.p("huodong/get_baoming_list", e);

// 普通用户获取活动报名列表
export const huodongget_baoming_list_public = e =>
  h.p("huodong/get_baoming_list_public", e);

// 添加收藏
export const huodongshoucang_add = e => h.p("huodong/shoucang_add", e);

// 删除收藏
export const huodongshoucang_del = e => h.p("huodong/shoucang_del", e);

// 点赞
export const huodongzan_add = e => h.p("huodong/zan_add", e);

// 取消点赞
export const huodongzan_del = e => h.p("huodong/zan_del", e);

// 添加评价
export const huodongadd_pingjia = e => h.p("huodong/add_pingjia", e);

// 获取评价
export const huodongget_pingjia = e => h.p("huodong/get_pingjia", e);

// 回答提问
export const huodongreply_pingjia = e => h.p("huodong/reply_pingjia", e);

// 获取轮播图
export const lunbotuindex = e => h.p("lunbotu/index", e);

// 微信支付
export const payweixin_pay = e => h.p("pay/weixin_pay", e);

// 微信签名
export const payget_weixinpay_sign = e => h.p("pay/get_weixinpay_sign", e);

// 余额支付
export const payyue_pay = e => h.p("pay/yue_pay", e);

// 注册登录
export const userlogin = e => h.p("user/login", e);

// 获取用户信息
export const userget_user_info = e => h.p("user/get_user_info", e);

// 修改资料
export const userupdate = e => h.p("user/update", e);

// 添加标签
export const useradd_label = e => h.p("user/add_label", e);

// 删除标签
export const userdel_label = e => h.p("user/del_label", e);

// 添加资料照片
export const useradd_img = e => h.p("user/add_img", e);

// 删除资料照片
export const userdel_img = e => h.p("user/del_img", e);

// 添加收货地址
export const useradd_addr = e => h.p("user/add_addr", e);

// 删除收货地址
export const userdel_addr = e => h.p("user/del_addr", e);

// 设置默认收货地址
export const userset_default_addr = e => h.p("user/set_default_addr", e);

// 获取收货地址列表
export const userget_addr_list = e => h.p("user/get_addr_list", e);

// 添加收款信息
export const userbank_add = e => h.p("user/bank_add", e);

// 删除收款账号信息
export const userbank_del = e => h.p("user/bank_del", e);

// 获取收款账户信息
export const userbank_list = e => h.p("user/bank_list", e);

// 设置默认收款地址
export const userset_default_bank = e => h.p("user/set_default_bank", e);

// 提现
export const usertixian = e => h.p("user/tixian", e);

// 获取提现记录
export const userget_tixian_list = e => h.p("user/get_tixian_list", e);

// 获取佣金
export const userget_yongjin_log = e => h.p("user/get_yongjin_log", e);

// ==================== 分会长运营体系相关API ====================

// 申请成为分会长
export const branch_presidentapply = e => h.p("Branchpresident/apply", e);

// 获取待审核活动列表
export const branch_presidentpending_activities = e => h.p("Branchpresident/pending_activities", e);

// 审核活动
export const branch_presidentreview_activity = e => h.p("Branchpresident/review_activity", e);

// 获取分会成员列表
export const branch_presidentget_members = e => h.p("Branchpresident/get_members", e);

// 获取分会活动列表
export const branch_presidentget_activities = e => h.p("Branchpresident/get_activities", e);

// 获取分会长运营佣金记录
export const branch_presidentget_commission = e => h.p("Branchpresident/get_commission", e);

// 获取分会长统计数据
export const branch_presidentget_stats = e => h.p("Branchpresident/get_stats", e);

// 获取分会长信息
export const getBranchLeaderInfo = e => h.p("user/get_branch_leader_info", e);

// 获取充值记录
export const userget_chongzhi_log = e => h.p("user/get_chongzhi_log", e);

// 获取分享记录
export const userget_fenxiang_log = e => h.p("user/get_fenxiang_log", e);

// 分享事件上传
export const userfenxiang_event = e => h.p("user/fenxiang_event", e);

// 获取对账单
export const userget_zhangdan = e => h.p("user/get_zhangdan", e);

// 充值下单
export const useradd_chongzhi_order = e => h.p("user/add_chongzhi_order", e);

// 购买会员下单
export const useradd_huiyuan_order = e => h.p("user/add_huiyuan_order", e);

// 添加关注
export const userguanzhu_add = e => h.p("user/guanzhu_add", e);

// 取消关注
export const userguanzhu_del = e => h.p("user/guanzhu_del", e);

// 检测关注
export const userguanzhu_check = e => h.p("user/guanzhu_check", e);

// 获取关注列表
export const userget_guanzhu_list = e => h.p("user/get_guanzhu_list", e);

// 获取社交用户列表
export const userget_shejiao_list = e => h.p("user/get_shejiao_list", e);

// 获取他人信息资料
export const userget_other_user_info = e => h.p("user/get_other_user_info", e);

// 获取粉丝列表
export const userget_fans_list = e => h.p("user/get_fans_list", e);

// 获取用户点赞内容
export const userget_user_likes = e => h.p("user/get_user_likes", e);

// 获取用户收藏内容
export const userget_user_favorites = e => h.p("user/get_user_favorites", e);

// 获取用户发布内容
export const userget_user_published = e => h.p("user/get_user_published", e);

// 获取用户评论内容
export const userget_user_comments = e => h.p("user/get_user_comments", e);

// 获取分享链接
export const configget_deep_link = e => h.p("config/get_deep_link", e);

// 获取大礼包详情
export const dalibaoget_info = e => h.p("dalibao/get_info", e);

// 大礼包下单
export const dalibaoadd_order = e => h.p("dalibao/add_order", e);

// 取消订单
export const dalibaocancel_order = e => h.p("dalibao/cancel_order", e);

// 确认收货
export const dalibaoqueren_shouhuo = e => h.p("dalibao/queren_shouhuo", e);

// 订单列表
export const dalibaoget_order_list = e => h.p("dalibao/get_order_list", e);

// APP相关配置
export const configpop = () => h.g("config/pop");

// 清空头像昵称
export const clearInfo = e => h.p("config/clear", e);

// 获取下级用户
export const userget_xiaji_user = e => h.p("user/get_xiaji_user", e);

// 获取账户待结算状态
export const userget_daijiesuan_status = e =>
  h.p("user/get_daijiesuan_status", e);

// 申请佣金提现
export const apply_commission_withdraw = e =>
  h.p("user/apply_commission_withdraw", e);

// 获取待结算活动列表
export const userget_daijiesuan_order_huodong = e =>
  h.p("user/get_daijiesuan_order_huodong", e);

// 获取待结算活动佣金订单列表
export const userget_daijiesuan_order_huodong_yongjin = e =>
  h.p("user/get_daijiesuan_order_huodong_yongjin", e);

// 获取待结算商品佣金订单列表
export const userget_daijiesuan_order_goods_yongjin = e =>
  h.p("user/get_daijiesuan_order_goods_yongjin", e);

// 获取待结算会员佣金订单列表
export const userget_daijiesuan_order_huiyuan_yongjin = e =>
  h.p("user/get_daijiesuan_order_huiyuan_yongjin", e);

// 获取二维码图片二进制
export const configqrcode = e => h.aB("config/qrcode", e);

// 世界模块API
// 获取日卡列表
export const getCards = e => h.p("world/get_cards", e);

// 获取动态列表
export const getFeeds = e => h.p("world/get_feeds", e);

// 获取动态详情
export const getFeedDetail = e => h.p("world/get_feed_detail", e);

// 发布动态
export const publishFeed = e => h.p("world/publish_feed", e);

// 发布日卡
export const publishCard = e => h.p("world/publish_card", e);

// 点赞动态
export const likeFeed = e => h.p("world/like_feed", e);

// 评论动态
export const commentFeed = e => h.p("world/comment_feed", e);

//获取指定日期范围的日卡数据
export const getDailyCards = e => h.p("world/get_daily_cards",e);

// 获取单张日卡详情
export const getCardDetail = e => h.p("world/get_card_detail", e);

// 点赞/取消点赞日卡
export const likeCard = e => h.p("world/like_card", e);

// 收藏/取消收藏日卡
export const favoriteCard = e => h.p("world/favorite_card", e);

// 收藏/取消收藏动态
export const favoriteFeed = e => h.p("world/favorite_feed", e);

// 评论日卡
export const commentCard = e => h.p("world/comment_card", e);

// 获取日卡评论列表
export const getCardComments = e => h.p("world/get_card_comments", e);

// 获取日记评论列表
export const getFeedComments = e => h.p("world/get_feed_comments", e);

// 点赞评论
export const likeComment = e => {
  // 确保将comment_id和uid/token参数名称匹配后端API要求
  const params = {
    ...e,
    comment_id: e.id || e.comment_id,  // 确保使用comment_id参数名
    uid: e.uid,
    token: e.token
  };
  return h.p("world/like_comment", params);
};

// 获取评论点赞状态
export const getCommentLikeStatus = e => h.p("world/get_comment_like_status", e);

// --- Quote APIs ---
// 创建摘录
export const createQuote = e => h.p("world/create_quote", e);
export const publishQuote = e => h.p("world/create_quote", e);

// {{ AURA-X: Add - 新增作者和出处管理API. Confirmed via 寸止 }}
// --- Author APIs ---
// 搜索作者
export const searchAuthors = e => h.p("world/search_authors", e);

// 创建作者
export const createAuthor = e => h.p("world/create_author", e);

// --- Source APIs ---
// 搜索出处
export const searchSources = e => h.p("world/search_sources", e);

// 创建出处
export const createSource = e => h.p("world/create_source", e);

// 获取作者详情
export const getAuthorDetail = e => h.p("world/get_author_detail", e);

// 获取出处详情
export const getSourceDetail = e => h.p("world/get_source_detail", e);

// 软删除作者
export const softDeleteAuthor = e => h.p("world/soft_delete_author", e);

// 软删除出处
export const softDeleteSource = e => h.p("world/soft_delete_source", e);

// 恢复作者
export const restoreAuthor = e => h.p("world/restore_author", e);

// 恢复出处
export const restoreSource = e => h.p("world/restore_source", e);

// 审核通过作者
export const approveAuthor = e => h.p("world/approve_author", e);

// 审核拒绝作者
export const rejectAuthor = e => h.p("world/reject_author", e);

// 审核通过出处
export const approveSource = e => h.p("world/approve_source", e);

// 审核拒绝出处
export const rejectSource = e => h.p("world/reject_source", e);

// 获取待审核作者列表
export const getPendingAuthors = e => h.p("world/get_pending_authors", e);

// 获取待审核出处列表
export const getPendingSources = e => h.p("world/get_pending_sources", e);

// 检查作者重复
export const checkAuthorDuplicates = e => h.p("world/check_author_duplicates", e);

// 检查出处重复
export const checkSourceDuplicates = e => h.p("world/check_source_duplicates", e);

// 编辑作者
export const updateAuthor = e => h.p("world/update_author", e);

// 编辑出处
export const updateSource = e => h.p("world/update_source", e);

// 获取摘录列表 - 统一命名
export const getQuotes = e => h.p("world/get_quotes", e);
export const getQuoteList = e => h.p("world/get_quotes", {
  ...e,
  page_size: e.pageSize || e.page_size || 20  // 参数名转换
});

// 获取摘录详情
export const getQuoteDetail = e => h.p("world/get_quote_detail", e);

// 点赞摘录
export const likeQuote = e => h.p("world/like_quote", e);

// 收藏摘录
export const favoriteQuote = e => h.p("world/favorite_quote", e);

// 获取摘录评论
export const getQuoteComments = e => h.p("world/get_quote_comments", e);

// 发布摘录评论
export const postQuoteComment = e => h.p("world/comment_quote", e);


// 获取日卡详情页随机摘录
export const getRandomQuotesForCard = e => h.p("world/get_random_quotes_for_card", e);

// --- Diary APIs ---
// 发布日记
export const publishDiary = e => h.p("world/publish_feed", { ...e, type: 'diary' });

// 获取日记列表 - 统一命名
export const getDiaries = e => h.p("world/get_feeds", { ...e, type: 'diary' });
export const getDiaryList = e => h.p("world/get_feeds", {
  ...e,
  type: 'diary',
  page_size: e.pageSize || e.page_size || 10,  // 参数名转换
  filter_type: e.filter_type || 'latest'  // 添加筛选类型
});

// 获取日记详情
export const getDiaryDetail = e => h.p("world/get_feed_detail", e);

// 删除日记
export const deleteDiary = e => h.p("world/delete_feed", e);

// 编辑日记
export const editDiary = e => h.p("world/edit_diary", e);

// --- 通知相关API ---
// 获取用户通知列表
export const userget_notifications = e => h.p("user/get_notifications", e);

// 标记通知为已读
export const usermark_notification_read = e => h.p("user/mark_notification_read", e);

// 获取未读通知数量
export const userget_unread_count = e => h.p("user/get_unread_count", e);

// 发送通知
export const sendNotification = e => h.p("user/send_notification", e);

// 创建全局通知（管理员专用）
export const usercreate_global_notification = e => h.p("user/create_global_notification", e);

// --- 会员分享相关API ---
// 创建体验会员分享链接
export const usercreate_trial_share = e => h.p("user/create_trial_share", e);

// 获取分享链接信息
export const userget_trial_info = e => h.p("user/get_trial_info", e);

// 领取体验会员
export const userclaim_trial_member = e => h.p("user/claim_trial_member", e);

// 获取分享记录
export const userget_share_records = e => h.p("user/get_share_records", e);

// 签到相关API
export const huodongcheckin = e => h.p("huodong/checkin", e);

// 积分相关API
export const userget_points_log = e => h.p("user/get_points_log", e);

// 删除动态
export const deleteFeed = e => h.p("world/delete_feed", e);

// 编辑动态
export const editFeed = e => h.p("world/edit_feed", e);

// 删除评论
export const deleteComment = e => h.p("world/delete_comment", e);

// 🆕 活动收入相关API
// 获取活动收入状态
export const get_activity_income_status = e => h.p("user/get_activity_income_status", e);

// 申请活动收入提现
export const apply_activity_income_withdraw = e => h.p("user/apply_activity_income_withdraw", e);

// 获取活动收入记录列表
export const get_activity_income_list = e => h.p("user/get_activity_income_list", e);


// 获取活动相册图片列表
export const huodongget_activity_photos = e => h.p("huodong/get_activity_photos", e);

// 上传活动相册图片
export const huodongupload_activity_photo = e => h.p("huodong/upload_activity_photo", e);

// 删除活动相册图片
export const huodongdelete_activity_photo = e => h.p("huodong/delete_activity_photo", e);